//
//  ShortcutTableCellView.swift
//  Switchman
//
//  Created by <PERSON> on 6/1/16.
//  Copyright © 2016 Alvin<PERSON><PERSON>. All rights reserved.
//

import Cocoa
import KeyboardShortcuts

class ShortcutTableCellView: NSTableCellView {

    @IBOutlet weak var shortcutView: NSView!
    private var recorderView: KeyboardShortcuts.RecorderCocoa?

    func configure(_ name: String,
                   icon: NSImage?,
                   shortcutName: KeyboardShortcuts.Name,
                   shortcutValueChange: @escaping () -> Void) {
        textField?.stringValue = name
        imageView?.image = icon

        // Remove existing recorder if any
        recorderView?.removeFromSuperview()

        // Create new recorder
        recorderView = KeyboardShortcuts.RecorderCocoa(for: shortcutName) {shortcut in 
            shortcutValueChange()
        }

        if let recorder = recorderView {
            shortcutView.addSubview(recorder)
            recorder.translatesAutoresizingMaskIntoConstraints = false
            NSLayoutConstraint.activate([
                recorder.leadingAnchor.constraint(equalTo: shortcutView.leadingAnchor),
                recorder.trailingAnchor.constraint(equalTo: shortcutView.trailingAnchor),
                recorder.topAnchor.constraint(equalTo: shortcutView.topAnchor),
                recorder.bottomAnchor.constraint(equalTo: shortcutView.bottomAnchor)
            ])
        }
    }

}
