//
//  ShortcutMonitor.swift
//  Switchman
//
//  Created by <PERSON> on 5/14/16.
//  Copyright © 2016 AlvinZhu. All rights reserved.
//

import Foundation
import Cocoa
import KeyboardShortcuts

struct ShortcutMonitor {

    // Store the listeners so we can manage them
    private static var listeners: [KeyboardShortcuts.Name: Any] = [:]

    static func register() {
        // First unregister any existing listeners
        unregister()

        let apps = AppsManager.manager.selectedApps
        for app in apps where app.shortcut != nil {
            let shortcutName = app.shortcutName

            // Create a listener for this app's shortcut
            let listener = KeyboardShortcuts.onKeyUp(for: shortcutName) {
                guard defaults[.EnableShortcut] else { return }

                // Hide App Switcher if it's visible when an app shortcut is used
                sharedAppDelegate?.hideAppSwitcher()

                if let frontmostAppIdentifier = NSWorkspace.shared.frontmostApplication?.bundleIdentifier,
                    let targetAppIdentifier = Bundle(url: app.appBundleURL)?.bundleIdentifier,
                    frontmostAppIdentifier == targetAppIdentifier {
                    NSRunningApplication.runningApplications(withBundleIdentifier: frontmostAppIdentifier).first?.hide()
                } else {
                    if #available(macOS 10.15, *) {
                        let configuration = NSWorkspace.OpenConfiguration()
                        configuration.activates = true
                        NSWorkspace.shared.openApplication(at: app.appBundleURL,
                                                           configuration: configuration) { _, error in
                            if let error = error {
                                NSLog("ERROR: \(error)")
                            }
                        }
                    } else {
                        NSWorkspace.shared.launchApplication(app.appBundleURL.lastPathComponent)
                    }
                }
            }

            // Store the listener so we can remove it later
            listeners[shortcutName] = listener
        }
    }

    static func unregister() {
        // KeyboardShortcuts automatically manages listeners, but we clear our references
        listeners.removeAll()
    }

}
