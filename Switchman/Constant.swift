//
//  Constant.swift
//  Switchman
//
//  Created by <PERSON><PERSON><PERSON> on 4/18/16.
//  Copyright © 2016 <PERSON><PERSON><PERSON>. All rights reserved.
//

import Cocoa

let sharedAppDelegate        = NSApplication.shared.delegate as? AppDelegate
let titleItemWidth: CGFloat  = 40.0
let titleItemHeight: CGFloat = 22.0

let launcherAppId = "org.gewill.Switchman.LauncherApplication"

// identifiers
let titleViewIdentifier             = NSToolbarItem.Identifier("titleViewIdentifier")
let appsTitleItemIdentifier         = NSToolbarItem.Identifier("appsTitleItemIdentifier")
let settingsTitleItemIdentifier     = NSToolbarItem.Identifier("settingsTitleItemIdentifier")
let shortcutTableCellViewIdentifier = "ShortcutTableCellView"

// Color Set
let windowbackgroundColorName = "WindowBackgroundColor"

// Notifications
extension Notification.Name {
    static let killLauncher = Notification.Name("killLauncher")
    static let updateMenuBarToggleState = Notification.Name("updateMenuBarToggleState")
}
