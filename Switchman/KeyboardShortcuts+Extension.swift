//
//  KeyboardShortcuts+Extension.swift
//  Switchman
//
//  Created by Augment Agent on 7/22/25.
//  Copyright © 2025 Alvin<PERSON>hu. All rights reserved.
//

import Foundation
import KeyboardShortcuts

extension KeyboardShortcuts.Name {
    // Dynamic shortcuts for apps - these will be created dynamically
    // based on the app bundle identifiers

    // Static shortcuts
    static let toggleMenuBarIcon = Self("toggleMenuBarIcon", default: .init(.t, modifiers: [.shift, .control, .option, .command]))
    static let showAppSwitcher = Self("showAppSwitcher", default: .init(.tab, modifiers: [.option]))
}

// Helper extension to create dynamic shortcut names for apps
extension KeyboardShortcuts.Name {
    static func appShortcut(for bundleIdentifier: String) -> Self {
        return Self("app_\(bundleIdentifier)")
    }
}
