//
//  String+Extension.swift
//  Switchman
//
//  Created by <PERSON><PERSON><PERSON> on 4/29/16.
//  Copyright © 2016 Alvin<PERSON><PERSON>. All rights reserved.
//

import Foundation

extension String {

    func localized() -> String {
        return NSLocalizedString(self, comment: "")
    }

    func appendingPathComponent(_ str: String) -> String {
        return (self as NSString).appendingPathComponent(str)
    }

}
