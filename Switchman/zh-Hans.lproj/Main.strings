
/* Class = "NSMenuItem"; title = "Switchman"; ObjectID = "1Xt-HY-uBw"; */
"1Xt-HY-uBw.title" = "Switchman";

/* Class = "NSTextFieldCell"; title = "Reactivate after:"; ObjectID = "1fT-Qh-dE2"; */
"1fT-Qh-dE2.title" = "多久后重新开启:";

/* Class = "NSButtonCell"; title = "Quit Switchman"; ObjectID = "2nX-9N-OOj"; */
"2nX-9N-OOj.title" = "退出 Switchman";

/* Class = "NSMenuItem"; title = "Quit Switchman"; ObjectID = "4sb-4s-VLi"; */
"4sb-4s-VLi.title" = "退出 Switchman";

/* Class = "NSMenu"; title = "Main Menu"; ObjectID = "AYu-sK-qS6"; */
"AYu-sK-qS6.title" = "主窗口";

/* Class = "NSMenu"; title = "OtherViews"; ObjectID = "Hcy-Ge-ZoW"; */
"Hcy-Ge-ZoW.title" = "其它视图";

/* Class = "NSWindow"; title = "Window"; ObjectID = "IQv-IB-iLA"; */
"IQv-IB-iLA.title" = "窗口";

/* Class = "NSMenuItem"; title = "Show All"; ObjectID = "Kd2-mp-pUS"; */
"Kd2-mp-pUS.title" = "显示全部";

/* Class = "NSMenuItem"; title = "Bring All to Front"; ObjectID = "LE2-aR-0XJ"; */
"LE2-aR-0XJ.title" = "前置全部窗口";

/* Class = "NSMenuItem"; title = "Preference"; ObjectID = "LOh-9N-Sq4"; */
"LOh-9N-Sq4.title" = "设置快捷键";

/* Class = "NSMenuItem"; title = "⌃ Control Key"; ObjectID = "NL4-zo-fys"; */
"NL4-zo-fys.title" = "⌃ Control 键";

/* Class = "NSMenuItem"; title = "Minimize"; ObjectID = "OY7-WF-poV"; */
"OY7-WF-poV.title" = "最小化";

/* Class = "NSMenuItem"; title = "Hide Switchman"; ObjectID = "Olw-nP-bQN"; */
"Olw-nP-bQN.title" = "隐藏 Switchman";

/* Class = "NSTextFieldCell"; title = "Text Cell"; ObjectID = "Orv-4t-jpB"; */
"Orv-4t-jpB.title" = "Text Cell";

/* Class = "NSMenuItem"; title = "Zoom"; ObjectID = "R4o-n2-Eq4"; */
"R4o-n2-Eq4.title" = "最大化";

/* Class = "NSTextFieldCell"; title = "0.1s"; ObjectID = "RmK-si-Zsx"; */
"RmK-si-Zsx.title" = "0.1秒";

/* Class = "NSMenuItem"; title = "Quit"; ObjectID = "RwK-Ds-RBj"; */
"RwK-Ds-RBj.title" = "退出";

/* Class = "NSMenu"; title = "Window"; ObjectID = "Td7-aD-5lo"; */
"Td7-aD-5lo.title" = "窗口";

/* Class = "NSTextFieldCell"; title = "1s"; ObjectID = "UpG-OL-d8P"; */
"UpG-OL-d8P.title" = "1秒";

/* Class = "NSButtonCell"; title = "Launch Switchman at login"; ObjectID = "Uwv-xu-Spi"; */
"Uwv-xu-Spi.title" = "开机后启动 Switchman";

/* Class = "NSMenuItem"; title = "Hide Others"; ObjectID = "Vdr-fp-XzO"; */
"Vdr-fp-XzO.title" = "隐藏其它应用";

/* Class = "NSMenuItem"; title = "⌘ Command Key"; ObjectID = "Yef-Cj-gO1"; */
"Yef-Cj-gO1.title" = "⌘ Command 键";

/* Class = "NSMenuItem"; title = "Window"; ObjectID = "aUF-d1-5bR"; */
"aUF-d1-5bR.title" = "窗口";

/* Class = "NSMenuItem"; title = "Privacy Policy"; ObjectID = "NJs-cX-AgP"; */
"NJs-cX-AgP.title" = "隐私政策";

/* Class = "NSMenuItem"; title = "Check for updates"; ObjectID = "b8r-Yq-V8b"; */
"b8r-Yq-V8b.title" = "检查更新";

/* Class = "NSButtonCell"; title = "Enable shortcut"; ObjectID = "fya-Hu-agS"; */
"fya-Hu-agS.title" = "开启快捷键";

/* Class = "NSMenuItem"; title = "⌥ Option Key"; ObjectID = "ltM-LE-FOy"; */
"ltM-LE-FOy.title" = "⌥ Option 键";

/* Class = "NSMenuItem"; title = "Version"; ObjectID = "ong-Un-zmC"; */
"ong-Un-zmC.title" = "版本号";

/* Class = "NSMenuItem"; title = "⇧ Shift Key"; ObjectID = "qJu-sX-5Pd"; */
"qJu-sX-5Pd.title" = "⇧ Shift 键";

/* Class = "NSTextFieldCell"; title = "Table View Cell"; ObjectID = "qh3-lo-MgL"; */
"qh3-lo-MgL.title" = "Table View Cell";

/* Class = "NSMenu"; title = "Switchman"; ObjectID = "uQy-DD-JDr"; */
"uQy-DD-JDr.title" = "Switchman";

/* Class = "NSTextFieldCell"; title = "Deactivate Key:"; ObjectID = "uWp-Ys-dhU"; */
"uWp-Ys-dhU.title" = "解除激活键:";

/* Class = "NSTextFieldCell"; title = "Double tap to deactivate the shortcuts"; ObjectID = "ntn-c3-HAA"; */
"ntn-c3-HAA.title" = "启用解除激活键，双击后临时关闭快捷键";

/* Class = "NSMenuItem"; title = "Export Shortcuts"; ObjectID = "1jO-A6-scN"; */
"1jO-A6-scN.title" = "导出快捷键";

/* Class = "NSMenuItem"; title = "Import Shortcuts"; ObjectID = "QQw-cp-cqP"; */
"QQw-cp-cqP.title" = "导入快捷键";

/* Class = "NSMenuItem"; title = "Show Switchman in the menu bar"; ObjectID = "LbD-19-0Ga"; */
"LbD-19-0Ga.title" = "在状态栏显示 Switchman 图标";

/* Class = "NSButtonCell"; title = "Enable ⇧⌃⌥ ⌘ + T to show/hide Switchman in the menu bar"; ObjectID = "kOR-Gg-12E"; */
"kOR-Gg-12E.title" = "启用 ⇧⌃⌥ ⌘ + T 在状态栏切换显示/隐藏 Switchman 图标";
