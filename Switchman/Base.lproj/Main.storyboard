<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.Cocoa.Storyboard.XIB" version="3.0" toolsVersion="24112" targetRuntime="MacOSX.Cocoa" propertyAccessControl="none" useAutolayout="YES">
    <dependencies>
        <deployment identifier="macosx"/>
        <plugIn identifier="com.apple.InterfaceBuilder.CocoaPlugin" version="24112"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--Application-->
        <scene sceneID="JPo-4y-FX3">
            <objects>
                <application id="hnw-xV-0zn" sceneMemberID="viewController">
                    <menu key="mainMenu" title="Main Menu" systemMenu="main" id="AYu-sK-qS6">
                        <items>
                            <menuItem title="Switchman" id="1Xt-HY-uBw">
                                <modifierMask key="keyEquivalentModifierMask"/>
                                <menu key="submenu" title="Switchman" systemMenu="apple" id="uQy-DD-JDr">
                                    <items>
                                        <menuItem title="Hide Switchman" keyEquivalent="h" id="Olw-nP-bQN">
                                            <connections>
                                                <action selector="hide:" target="Ady-hI-5gd" id="PnN-Uc-m68"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Hide Others" keyEquivalent="h" id="Vdr-fp-XzO">
                                            <modifierMask key="keyEquivalentModifierMask" option="YES" command="YES"/>
                                            <connections>
                                                <action selector="hideOtherApplications:" target="Ady-hI-5gd" id="VT4-aY-XCT"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Show All" id="Kd2-mp-pUS">
                                            <modifierMask key="keyEquivalentModifierMask"/>
                                            <connections>
                                                <action selector="unhideAllApplications:" target="Ady-hI-5gd" id="Dhg-Le-xox"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem isSeparatorItem="YES" id="kCx-OE-vgT"/>
                                        <menuItem title="Quit Switchman" keyEquivalent="q" id="4sb-4s-VLi">
                                            <connections>
                                                <action selector="terminate:" target="Ady-hI-5gd" id="Te7-pn-YzF"/>
                                            </connections>
                                        </menuItem>
                                    </items>
                                </menu>
                            </menuItem>
                            <menuItem title="Window" id="aUF-d1-5bR">
                                <modifierMask key="keyEquivalentModifierMask"/>
                                <menu key="submenu" title="Window" systemMenu="window" id="Td7-aD-5lo">
                                    <items>
                                        <menuItem title="Minimize" keyEquivalent="m" id="OY7-WF-poV">
                                            <modifierMask key="keyEquivalentModifierMask" control="YES" option="YES" command="YES"/>
                                            <connections>
                                                <action selector="performMiniaturize:" target="Ady-hI-5gd" id="VwT-WD-YPe"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Zoom" id="R4o-n2-Eq4">
                                            <modifierMask key="keyEquivalentModifierMask"/>
                                            <connections>
                                                <action selector="performZoom:" target="Ady-hI-5gd" id="DIl-cC-cCs"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem isSeparatorItem="YES" id="eu3-7i-yIM"/>
                                        <menuItem title="Bring All to Front" id="LE2-aR-0XJ">
                                            <modifierMask key="keyEquivalentModifierMask"/>
                                            <connections>
                                                <action selector="arrangeInFront:" target="Ady-hI-5gd" id="DRN-fu-gQh"/>
                                            </connections>
                                        </menuItem>
                                    </items>
                                </menu>
                            </menuItem>
                        </items>
                    </menu>
                    <connections>
                        <outlet property="delegate" destination="Voe-Tx-rLC" id="PrD-fu-P6m"/>
                    </connections>
                </application>
                <customObject id="Voe-Tx-rLC" customClass="AppDelegate" customModule="Switchman" customModuleProvider="target">
                    <connections>
                        <outlet property="statusItemController" destination="tdb-s2-QWr" id="Gmx-H6-CHo"/>
                    </connections>
                </customObject>
                <customObject id="tdb-s2-QWr" customClass="StatusItemController" customModule="Switchman" customModuleProvider="target">
                    <connections>
                        <outlet property="statusMenu" destination="dS2-uP-mFH" id="RK2-r1-FZR"/>
                        <outlet property="toggleEnableStateMenuItem" destination="p6e-3p-Tyr" id="wau-f6-Sj0"/>
                        <outlet property="updateMenuItem" destination="b8r-Yq-V8b" id="9wp-y4-abl"/>
                        <outlet property="versionMenuItem" destination="ong-Un-zmC" id="2gn-me-ivp"/>
                    </connections>
                </customObject>
                <menu id="dS2-uP-mFH">
                    <items>
                        <menuItem title="Preference" id="LOh-9N-Sq4">
                            <modifierMask key="keyEquivalentModifierMask"/>
                            <connections>
                                <action selector="showApps:" target="tdb-s2-QWr" id="zr3-M4-Xwv"/>
                            </connections>
                        </menuItem>
                        <menuItem title="Privacy Policy" id="NJs-cX-AgP">
                            <modifierMask key="keyEquivalentModifierMask"/>
                            <connections>
                                <action selector="privacyPolicy:" target="tdb-s2-QWr" id="Bw8-vJ-Pqg"/>
                            </connections>
                        </menuItem>
                        <menuItem title="Check for updates" id="b8r-Yq-V8b">
                            <modifierMask key="keyEquivalentModifierMask"/>
                        </menuItem>
                        <menuItem title="Version" enabled="NO" id="ong-Un-zmC">
                            <modifierMask key="keyEquivalentModifierMask"/>
                        </menuItem>
                        <menuItem isSeparatorItem="YES" id="yP6-mr-MRs"/>
                        <menuItem title="Disable Shortcuts" id="p6e-3p-Tyr">
                            <modifierMask key="keyEquivalentModifierMask"/>
                            <connections>
                                <action selector="toggleEnableState:" target="tdb-s2-QWr" id="iWk-U2-Khc"/>
                            </connections>
                        </menuItem>
                        <menuItem isSeparatorItem="YES" id="8ld-uy-wtv"/>
                        <menuItem title="Export Shortcuts" id="1jO-A6-scN">
                            <modifierMask key="keyEquivalentModifierMask"/>
                            <connections>
                                <action selector="exportShortcuts:" target="tdb-s2-QWr" id="tbJ-Lh-IyE"/>
                            </connections>
                        </menuItem>
                        <menuItem title="Import Shortcuts" id="QQw-cp-cqP">
                            <modifierMask key="keyEquivalentModifierMask"/>
                            <connections>
                                <action selector="importShortcuts:" target="tdb-s2-QWr" id="EOd-RJ-0kt"/>
                            </connections>
                        </menuItem>
                        <menuItem isSeparatorItem="YES" id="WRo-Lh-hhC"/>
                        <menuItem title="Quit" id="RwK-Ds-RBj">
                            <modifierMask key="keyEquivalentModifierMask"/>
                            <connections>
                                <action selector="quit:" target="tdb-s2-QWr" id="j95-qv-IKv"/>
                            </connections>
                        </menuItem>
                    </items>
                    <connections>
                        <outlet property="delegate" destination="tdb-s2-QWr" id="IHT-Nm-3FZ"/>
                    </connections>
                </menu>
                <customObject id="Ady-hI-5gd" userLabel="First Responder" customClass="NSResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="7.5" y="40"/>
        </scene>
        <!--View Controller-->
        <scene sceneID="S00-Mq-ioy">
            <objects>
                <viewController id="x6J-UP-P0f" sceneMemberID="viewController">
                    <view key="view" id="h6s-l2-XQH">
                        <rect key="frame" x="0.0" y="0.0" width="360" height="458"/>
                        <autoresizingMask key="autoresizingMask"/>
                    </view>
                </viewController>
                <customObject id="8T1-Ww-qhk" userLabel="First Responder" customClass="NSResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="577" y="416"/>
        </scene>
        <!--Window Controller-->
        <scene sceneID="R2V-B0-nI4">
            <objects>
                <windowController storyboardIdentifier="MainWindowController" showSeguePresentationStyle="single" id="B8D-0N-5wS" customClass="MainWindowController" customModule="Switchman" customModuleProvider="target" sceneMemberID="viewController">
                    <window key="window" title="Window" allowsToolTipsWhenApplicationIsInactive="NO" autorecalculatesKeyViewLoop="NO" restorable="NO" releasedWhenClosed="NO" visibleAtLaunch="NO" animationBehavior="default" id="IQv-IB-iLA">
                        <windowStyleMask key="styleMask" titled="YES" closable="YES" miniaturizable="YES"/>
                        <rect key="contentRect" x="100" y="240" width="360" height="480"/>
                        <rect key="screenRect" x="0.0" y="0.0" width="1680" height="1027"/>
                        <connections>
                            <outlet property="delegate" destination="B8D-0N-5wS" id="JWv-RT-bCs"/>
                        </connections>
                    </window>
                    <connections>
                        <segue destination="x6J-UP-P0f" kind="relationship" relationship="window.shadowedContentViewController" id="r77-hl-J1L"/>
                    </connections>
                </windowController>
                <customObject id="Oky-zY-oP4" userLabel="First Responder" customClass="NSResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="74" y="416"/>
        </scene>
        <!--Shortcut List View Controller-->
        <scene sceneID="hIz-AP-VOD">
            <objects>
                <customObject id="rPt-NT-nkU" userLabel="First Responder" customClass="NSResponder" sceneMemberID="firstResponder"/>
                <viewController storyboardIdentifier="ShortcutListViewController" id="XfG-lQ-9wD" customClass="ShortcutListViewController" customModule="Switchman" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" wantsLayer="YES" id="m2S-Jp-Qdl">
                        <rect key="frame" x="0.0" y="0.0" width="360" height="485"/>
                        <autoresizingMask key="autoresizingMask"/>
                        <subviews>
                            <button verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="PkA-I4-Eeo">
                                <rect key="frame" x="41" y="19" width="22" height="22"/>
                                <buttonCell key="cell" type="smallSquare" bezelStyle="smallSquare" image="NSRemoveTemplate" imagePosition="overlaps" alignment="center" lineBreakMode="truncatingTail" state="on" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="8g0-Nk-gkJ">
                                    <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                    <font key="font" metaFont="system"/>
                                </buttonCell>
                                <constraints>
                                    <constraint firstAttribute="height" constant="20" id="IXM-DO-CdA"/>
                                    <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="22" id="bRz-uc-Coo"/>
                                </constraints>
                                <connections>
                                    <action selector="remove:" target="XfG-lQ-9wD" id="f2C-kB-pIt"/>
                                </connections>
                            </button>
                            <button verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="Qsh-Hx-foM">
                                <rect key="frame" x="20" y="19" width="22" height="22"/>
                                <buttonCell key="cell" type="smallSquare" bezelStyle="smallSquare" image="NSAddTemplate" imagePosition="overlaps" alignment="center" lineBreakMode="truncatingTail" state="on" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="XXi-Qz-DcN">
                                    <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                    <font key="font" metaFont="system"/>
                                </buttonCell>
                                <constraints>
                                    <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="22" id="AsC-Zw-bWK"/>
                                    <constraint firstAttribute="height" constant="20" id="vYF-uQ-qrj"/>
                                </constraints>
                                <connections>
                                    <action selector="add:" target="XfG-lQ-9wD" id="N6L-b4-wcP"/>
                                </connections>
                            </button>
                            <scrollView autohidesScrollers="YES" horizontalLineScroll="46" horizontalPageScroll="10" verticalLineScroll="46" verticalPageScroll="10" hasHorizontalScroller="NO" usesPredominantAxisScrolling="NO" horizontalScrollElasticity="none" translatesAutoresizingMaskIntoConstraints="NO" id="fZm-UD-Mz7">
                                <rect key="frame" x="20" y="60" width="320" height="405"/>
                                <clipView key="contentView" ambiguous="YES" copiesOnScroll="NO" id="SoO-NZ-Dda">
                                    <rect key="frame" x="1" y="1" width="318" height="403"/>
                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                    <subviews>
                                        <tableView verticalHuggingPriority="750" ambiguous="YES" allowsExpansionToolTips="YES" columnAutoresizingStyle="none" alternatingRowBackgroundColors="YES" columnReordering="NO" columnSelection="YES" columnResizing="NO" multipleSelection="NO" autosaveColumns="NO" rowHeight="44" viewBased="YES" id="aOE-yu-2yD">
                                            <rect key="frame" x="0.0" y="0.0" width="318" height="403"/>
                                            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                            <size key="intercellSpacing" width="0.0" height="2"/>
                                            <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                            <color key="gridColor" name="gridColor" catalog="System" colorSpace="catalog"/>
                                            <tableColumns>
                                                <tableColumn identifier="AppTableCellView" width="306" maxWidth="318" id="TMx-gL-5FD">
                                                    <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border">
                                                        <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                                        <color key="backgroundColor" name="headerColor" catalog="System" colorSpace="catalog"/>
                                                    </tableHeaderCell>
                                                    <textFieldCell key="dataCell" lineBreakMode="truncatingTail" selectable="YES" editable="YES" title="Text Cell" id="Orv-4t-jpB">
                                                        <font key="font" metaFont="system"/>
                                                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                        <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                    </textFieldCell>
                                                    <prototypeCellViews>
                                                        <tableCellView identifier="ShortcutTableCellView" id="qGf-fE-yif" customClass="ShortcutTableCellView" customModule="Switchman" customModuleProvider="target">
                                                            <rect key="frame" x="0.0" y="1" width="318" height="44"/>
                                                            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                                            <subviews>
                                                                <imageView horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="NdX-eb-XMU">
                                                                    <rect key="frame" x="16" y="-1" width="36" height="47"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="height" constant="36" id="3V3-Ej-jcC"/>
                                                                        <constraint firstAttribute="width" constant="36" id="xh0-PN-S56"/>
                                                                    </constraints>
                                                                    <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyDown" image="NSActionTemplate" id="9xQ-n2-9Fa"/>
                                                                </imageView>
                                                                <textField verticalHuggingPriority="750" horizontalCompressionResistancePriority="250" allowsExpansionToolTips="YES" translatesAutoresizingMaskIntoConstraints="NO" id="rif-T7-VzL">
                                                                    <rect key="frame" x="58" y="14" width="116" height="16"/>
                                                                    <textFieldCell key="cell" lineBreakMode="truncatingTail" sendsActionOnEndEditing="YES" alignment="left" title="Table View Cell" id="qh3-lo-MgL">
                                                                        <font key="font" metaFont="system"/>
                                                                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                                                    </textFieldCell>
                                                                </textField>
                                                                <customView translatesAutoresizingMaskIntoConstraints="NO" id="swk-AA-IMy">
                                                                    <rect key="frame" x="182" y="13" width="120" height="19"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="width" constant="120" id="oDg-xm-2rZ"/>
                                                                        <constraint firstAttribute="height" constant="19" id="wrB-v0-XFs"/>
                                                                    </constraints>
                                                                </customView>
                                                            </subviews>
                                                            <constraints>
                                                                <constraint firstItem="rif-T7-VzL" firstAttribute="leading" secondItem="NdX-eb-XMU" secondAttribute="trailing" constant="8" id="52Z-ue-QAu"/>
                                                                <constraint firstItem="swk-AA-IMy" firstAttribute="centerY" secondItem="qGf-fE-yif" secondAttribute="centerY" id="77c-o9-V4g"/>
                                                                <constraint firstItem="swk-AA-IMy" firstAttribute="centerY" secondItem="qGf-fE-yif" secondAttribute="centerY" id="7QJ-rl-74N"/>
                                                                <constraint firstItem="NdX-eb-XMU" firstAttribute="leading" secondItem="qGf-fE-yif" secondAttribute="leading" constant="16" id="7cn-Oc-QAc"/>
                                                                <constraint firstAttribute="trailing" secondItem="swk-AA-IMy" secondAttribute="trailing" constant="16" id="Knl-JM-snN"/>
                                                                <constraint firstItem="swk-AA-IMy" firstAttribute="leading" secondItem="rif-T7-VzL" secondAttribute="trailing" constant="10" id="cPv-5J-CT4"/>
                                                                <constraint firstItem="rif-T7-VzL" firstAttribute="centerY" secondItem="qGf-fE-yif" secondAttribute="centerY" id="vms-xr-Veb"/>
                                                                <constraint firstItem="NdX-eb-XMU" firstAttribute="centerY" secondItem="qGf-fE-yif" secondAttribute="centerY" id="yBg-A3-UqU"/>
                                                            </constraints>
                                                            <connections>
                                                                <outlet property="imageView" destination="NdX-eb-XMU" id="LWd-iz-zdu"/>
                                                                <outlet property="shortcutView" destination="swk-AA-IMy" id="dBA-86-qPI"/>
                                                                <outlet property="textField" destination="rif-T7-VzL" id="Hl4-ch-6N6"/>
                                                            </connections>
                                                        </tableCellView>
                                                    </prototypeCellViews>
                                                </tableColumn>
                                            </tableColumns>
                                            <connections>
                                                <outlet property="dataSource" destination="XfG-lQ-9wD" id="ivQ-10-5my"/>
                                                <outlet property="delegate" destination="XfG-lQ-9wD" id="zb8-jF-vtz"/>
                                            </connections>
                                        </tableView>
                                    </subviews>
                                </clipView>
                                <constraints>
                                    <constraint firstAttribute="width" constant="320" id="yXZ-fs-vGy"/>
                                </constraints>
                                <scroller key="horizontalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" horizontal="YES" id="eym-iE-zkx">
                                    <rect key="frame" x="-100" y="-100" width="318" height="16"/>
                                    <autoresizingMask key="autoresizingMask"/>
                                </scroller>
                                <scroller key="verticalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" horizontal="NO" id="FaQ-iX-Sob">
                                    <rect key="frame" x="-100" y="-100" width="15" height="102"/>
                                    <autoresizingMask key="autoresizingMask"/>
                                </scroller>
                            </scrollView>
                        </subviews>
                        <constraints>
                            <constraint firstItem="fZm-UD-Mz7" firstAttribute="leading" secondItem="m2S-Jp-Qdl" secondAttribute="leading" constant="20" id="25v-CA-rNL"/>
                            <constraint firstAttribute="bottom" secondItem="Qsh-Hx-foM" secondAttribute="bottom" constant="20" id="2EM-0U-1PR"/>
                            <constraint firstItem="Qsh-Hx-foM" firstAttribute="top" secondItem="fZm-UD-Mz7" secondAttribute="bottom" constant="20" id="AVT-3W-0gH"/>
                            <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="PkA-I4-Eeo" secondAttribute="trailing" constant="20" symbolic="YES" id="ByS-AD-uKH"/>
                            <constraint firstItem="PkA-I4-Eeo" firstAttribute="leading" secondItem="Qsh-Hx-foM" secondAttribute="trailing" constant="-1" id="S9d-vN-569"/>
                            <constraint firstAttribute="trailing" secondItem="fZm-UD-Mz7" secondAttribute="trailing" constant="20" symbolic="YES" id="XDM-VO-X76"/>
                            <constraint firstAttribute="bottom" secondItem="PkA-I4-Eeo" secondAttribute="bottom" constant="20" id="dgq-WU-3LL"/>
                            <constraint firstItem="Qsh-Hx-foM" firstAttribute="leading" secondItem="m2S-Jp-Qdl" secondAttribute="leading" constant="20" id="oP5-0l-e1v"/>
                            <constraint firstItem="fZm-UD-Mz7" firstAttribute="top" secondItem="m2S-Jp-Qdl" secondAttribute="top" constant="20" id="vXD-lB-LlF"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="btnAdd" destination="Qsh-Hx-foM" id="kdq-Be-yT5"/>
                        <outlet property="btnRemove" destination="PkA-I4-Eeo" id="xuy-0y-Bmq"/>
                        <outlet property="tableView" destination="aOE-yu-2yD" id="bmS-Bh-9DW"/>
                    </connections>
                </viewController>
                <userDefaultsController id="C1B-Hp-Svg"/>
            </objects>
            <point key="canvasLocation" x="1047" y="415.5"/>
        </scene>
        <!--App Switcher View Controller-->
        <scene sceneID="App-Switcher-Scene">
            <objects>
                <customObject id="App-Switcher-First-Responder" userLabel="First Responder" customClass="NSResponder" sceneMemberID="firstResponder"/>
                <viewController storyboardIdentifier="AppSwitcherViewController" id="App-Switcher-VC" customClass="AppSwitcherViewController" customModule="Switchman" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" id="App-Switcher-View">
                        <rect key="frame" x="0.0" y="0.0" width="600" height="400"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                    </view>
                </viewController>
            </objects>
            <point key="canvasLocation" x="909" y="-180"/>
        </scene>
        <!--Settings View Controller-->
        <scene sceneID="Uye-Ur-Jj1">
            <objects>
                <customObject id="0cR-1e-8gt" userLabel="First Responder" customClass="NSResponder" sceneMemberID="firstResponder"/>
                <viewController storyboardIdentifier="SettingsViewController" id="LfM-H7-njo" customClass="SettingsViewController" customModule="Switchman" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" id="Crp-4s-mEo">
                        <rect key="frame" x="0.0" y="0.0" width="366" height="480"/>
                        <autoresizingMask key="autoresizingMask"/>
                        <subviews>
                            <button translatesAutoresizingMaskIntoConstraints="NO" id="XJy-eX-prE">
                                <rect key="frame" x="47" y="436" width="193" height="14"/>
                                <buttonCell key="cell" type="check" title="Launch Switchman at login" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="Uwv-xu-Spi">
                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                    <font key="font" size="13" name="STHeitiSC-Light"/>
                                </buttonCell>
                                <constraints>
                                    <constraint firstAttribute="height" constant="14" id="JOK-Hg-JZr"/>
                                </constraints>
                                <connections>
                                    <action selector="toggleLaunchAtLogin:" target="LfM-H7-njo" id="OBV-kl-ahX"/>
                                </connections>
                            </button>
                            <textField horizontalHuggingPriority="251" verticalHuggingPriority="751" translatesAutoresizingMaskIntoConstraints="NO" id="Dtr-0d-C8U">
                                <rect key="frame" x="45" y="128" width="104" height="16"/>
                                <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Reactivate after:" id="1fT-Qh-dE2">
                                    <font key="font" metaFont="system"/>
                                    <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                    <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                </textFieldCell>
                            </textField>
                            <slider verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="ySw-Wg-Pgf">
                                <rect key="frame" x="153" y="126" width="168" height="21"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="21" id="HaR-z1-X7a"/>
                                </constraints>
                                <sliderCell key="cell" state="on" alignment="left" maxValue="1" tickMarkPosition="below" numberOfTickMarks="10" allowsTickMarkValuesOnly="YES" sliderType="linear" id="9mT-en-uZb">
                                    <connections>
                                        <action selector="changeShortcutReactivateInterval:" target="LfM-H7-njo" id="V4e-FL-dTK"/>
                                    </connections>
                                </sliderCell>
                            </slider>
                            <button translatesAutoresizingMaskIntoConstraints="NO" id="7sy-3X-TvI">
                                <rect key="frame" x="47" y="387" width="120" height="14"/>
                                <buttonCell key="cell" type="check" title="Enable shortcut" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="fya-Hu-agS">
                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                    <font key="font" size="13" name="STHeitiSC-Light"/>
                                </buttonCell>
                                <constraints>
                                    <constraint firstAttribute="height" constant="14" id="gqi-u1-4Bj"/>
                                </constraints>
                                <connections>
                                    <action selector="toggleEnableShortcut:" target="LfM-H7-njo" id="wyA-Z5-TGh"/>
                                </connections>
                            </button>
                            <button verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="prA-Il-0qM">
                                <rect key="frame" x="90" y="20" width="186" height="24"/>
                                <buttonCell key="cell" type="push" title="Quit Switchman" bezelStyle="rounded" alignment="center" borderStyle="border" inset="2" id="2nX-9N-OOj">
                                    <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                    <font key="font" metaFont="system"/>
                                </buttonCell>
                                <connections>
                                    <action selector="exit:" target="LfM-H7-njo" id="4R9-rw-H8N"/>
                                </connections>
                            </button>
                            <textField horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="cIc-ga-45L">
                                <rect key="frame" x="45" y="164" width="99" height="16"/>
                                <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Deactivate Key:" id="uWp-Ys-dhU">
                                    <font key="font" metaFont="system"/>
                                    <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                    <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                </textFieldCell>
                            </textField>
                            <textField horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="hWC-9z-cv3">
                                <rect key="frame" x="151" y="109" width="32" height="16"/>
                                <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="center" title="0.1s" id="RmK-si-Zsx">
                                    <font key="font" metaFont="system"/>
                                    <color key="textColor" name="headerColor" catalog="System" colorSpace="catalog"/>
                                    <color key="backgroundColor" name="headerColor" catalog="System" colorSpace="catalog"/>
                                </textFieldCell>
                            </textField>
                            <textField horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="9pw-DH-pgZ">
                                <rect key="frame" x="302" y="107" width="21" height="16"/>
                                <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="center" title="1s" id="UpG-OL-d8P">
                                    <font key="font" metaFont="system"/>
                                    <color key="textColor" name="headerColor" catalog="System" colorSpace="catalog"/>
                                    <color key="backgroundColor" name="headerColor" catalog="System" colorSpace="catalog"/>
                                </textFieldCell>
                            </textField>
                            <popUpButton verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="0zk-yi-rDJ" userLabel="⌘ Command Key">
                                <rect key="frame" x="148" y="160" width="166" height="24"/>
                                <popUpButtonCell key="cell" type="push" title="⇧ Shift Key" bezelStyle="rounded" alignment="left" lineBreakMode="truncatingTail" state="on" borderStyle="borderAndBezel" imageScaling="proportionallyDown" inset="2" selectedItem="qJu-sX-5Pd" id="2rr-yT-VNc" userLabel="⌃ Control Key">
                                    <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                                    <font key="font" metaFont="message"/>
                                    <menu key="menu" title="OtherViews" id="Hcy-Ge-ZoW">
                                        <items>
                                            <menuItem title="⇧ Shift Key" state="on" id="qJu-sX-5Pd" userLabel="⇧ Shift Key">
                                                <modifierMask key="keyEquivalentModifierMask" shift="YES"/>
                                            </menuItem>
                                            <menuItem title="⌃ Control Key" id="NL4-zo-fys" userLabel="⌃ Control Key">
                                                <modifierMask key="keyEquivalentModifierMask" control="YES"/>
                                            </menuItem>
                                            <menuItem title="⌥ Option Key" id="ltM-LE-FOy" userLabel="⌥ Option Key">
                                                <modifierMask key="keyEquivalentModifierMask" option="YES"/>
                                            </menuItem>
                                            <menuItem title="⌘ Command Key" id="Yef-Cj-gO1" userLabel="⌘ Command Key"/>
                                        </items>
                                    </menu>
                                </popUpButtonCell>
                                <color key="contentTintColor" red="0.51764705882352935" green="0.51764705882352935" blue="0.51764705882352935" alpha="0.89803921568627454" colorSpace="calibratedRGB"/>
                                <connections>
                                    <action selector="changeDeactivateKey:" target="LfM-H7-njo" id="KCl-Bg-YRc"/>
                                </connections>
                            </popUpButton>
                            <button translatesAutoresizingMaskIntoConstraints="NO" id="App-Switcher-Enable-Btn">
                                <rect key="frame" x="47" y="251" width="153" height="14"/>
                                <buttonCell key="cell" type="check" title="Enable App Switcher" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="App-Switcher-Enable-Cell">
                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                    <font key="font" size="13" name="STHeitiSC-Light"/>
                                </buttonCell>
                                <constraints>
                                    <constraint firstAttribute="height" constant="14" id="App-Switcher-Enable-Height"/>
                                </constraints>
                                <connections>
                                    <action selector="toggleEnableAppSwitcher:" target="LfM-H7-njo" id="App-Switcher-Enable-Action"/>
                                </connections>
                            </button>
                            <view translatesAutoresizingMaskIntoConstraints="NO" id="App-Switcher-Recorder-View">
                                <rect key="frame" x="67" y="226" width="200" height="20"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="20" id="App-Switcher-Recorder-Height"/>
                                    <constraint firstAttribute="width" constant="200" id="App-Switcher-Recorder-Width"/>
                                </constraints>
                            </view>
                            <button translatesAutoresizingMaskIntoConstraints="NO" id="ych-hK-1qT">
                                <rect key="frame" x="47" y="200" width="291" height="16"/>
                                <buttonCell key="cell" type="check" title="Enable double tap to deactivate shortcuts" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="ntn-c3-HAA">
                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                    <font key="font" size="13" name="STHeitiSC-Light"/>
                                </buttonCell>
                                <connections>
                                    <action selector="toggleEnableDeactivateKey:" target="LfM-H7-njo" id="5Rn-lR-zu6"/>
                                </connections>
                            </button>
                            <button translatesAutoresizingMaskIntoConstraints="NO" id="d3U-w9-jNg">
                                <rect key="frame" x="47" y="338" width="232" height="14"/>
                                <buttonCell key="cell" type="check" title="Show Switchman in the menu bar" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="LbD-19-0Ga">
                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                    <font key="font" size="13" name="STHeitiSC-Light"/>
                                </buttonCell>
                                <constraints>
                                    <constraint firstAttribute="height" constant="14" id="V6n-1t-57Z"/>
                                </constraints>
                                <connections>
                                    <action selector="toggleEnableMenuBarIcon:" target="LfM-H7-njo" id="QqI-Ja-rEW"/>
                                </connections>
                            </button>
                            <button translatesAutoresizingMaskIntoConstraints="NO" id="Lcx-bw-woE" userLabel="Btn Enable Menu Bar Icon Show/Hide Key">
                                <rect key="frame" x="47" y="275" width="290" height="28"/>
                                <buttonCell key="cell" type="check" title="Enable ⇧⌃⌥ ⌘ + T to show/hide Switchman in the menu bar" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="kOR-Gg-12E" userLabel="Enable ⇧⌃⌥ ⌘ + T to show/hide Thor in the menu bar">
                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                    <font key="font" size="13" name="STHeitiSC-Light"/>
                                </buttonCell>
                                <constraints>
                                    <constraint firstAttribute="width" constant="290" id="IPY-Ir-dXy"/>
                                    <constraint firstAttribute="height" constant="28" id="JHI-wJ-c39"/>
                                </constraints>
                                <connections>
                                    <action selector="toggleEnableMenuBarIconShowHideKey:" target="LfM-H7-njo" id="jTf-bs-bOM"/>
                                </connections>
                            </button>
                        </subviews>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="0zk-yi-rDJ" secondAttribute="trailing" constant="52" id="0pu-kB-MFi"/>
                            <constraint firstItem="ySw-Wg-Pgf" firstAttribute="leading" secondItem="Dtr-0d-C8U" secondAttribute="trailing" constant="6" id="0qn-xl-8i7"/>
                            <constraint firstItem="prA-Il-0qM" firstAttribute="leading" secondItem="Crp-4s-mEo" secondAttribute="leading" constant="90" id="1Tx-pu-Mto"/>
                            <constraint firstItem="9pw-DH-pgZ" firstAttribute="top" secondItem="ySw-Wg-Pgf" secondAttribute="bottom" constant="3" id="1r2-IW-hCj"/>
                            <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="d3U-w9-jNg" secondAttribute="trailing" constant="20" symbolic="YES" id="7A4-et-3p2"/>
                            <constraint firstItem="XJy-eX-prE" firstAttribute="leading" secondItem="7sy-3X-TvI" secondAttribute="leading" id="8yE-nG-3YR"/>
                            <constraint firstItem="cIc-ga-45L" firstAttribute="leading" secondItem="Dtr-0d-C8U" secondAttribute="leading" id="ABh-Kh-94o"/>
                            <constraint firstItem="prA-Il-0qM" firstAttribute="centerX" secondItem="Crp-4s-mEo" secondAttribute="centerX" id="Afz-rC-qYb"/>
                            <constraint firstItem="App-Switcher-Enable-Btn" firstAttribute="leading" secondItem="7sy-3X-TvI" secondAttribute="leading" id="App-Switcher-Leading"/>
                            <constraint firstItem="App-Switcher-Recorder-View" firstAttribute="leading" secondItem="App-Switcher-Enable-Btn" secondAttribute="leading" constant="20" id="App-Switcher-Recorder-Leading"/>
                            <constraint firstItem="App-Switcher-Recorder-View" firstAttribute="top" secondItem="App-Switcher-Enable-Btn" secondAttribute="bottom" constant="5" id="App-Switcher-Recorder-Top"/>
                            <constraint firstItem="App-Switcher-Enable-Btn" firstAttribute="top" secondItem="Lcx-bw-woE" secondAttribute="bottom" constant="10" id="App-Switcher-Top"/>
                            <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="App-Switcher-Enable-Btn" secondAttribute="trailing" constant="20" symbolic="YES" id="App-Switcher-Trailing"/>
                            <constraint firstAttribute="trailing" secondItem="ySw-Wg-Pgf" secondAttribute="trailing" constant="45" id="Icv-p1-Eqd"/>
                            <constraint firstItem="ySw-Wg-Pgf" firstAttribute="leading" secondItem="hWC-9z-cv3" secondAttribute="leading" id="KGC-X6-H3j"/>
                            <constraint firstItem="Dtr-0d-C8U" firstAttribute="centerY" secondItem="ySw-Wg-Pgf" secondAttribute="centerY" id="KjM-fA-ZVu"/>
                            <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="XJy-eX-prE" secondAttribute="trailing" constant="20" symbolic="YES" id="L9K-CH-0wd"/>
                            <constraint firstItem="XJy-eX-prE" firstAttribute="top" secondItem="Crp-4s-mEo" secondAttribute="top" constant="30" id="Mm2-Om-20p"/>
                            <constraint firstItem="Dtr-0d-C8U" firstAttribute="top" secondItem="cIc-ga-45L" secondAttribute="bottom" constant="20" id="OUS-qx-t0k"/>
                            <constraint firstItem="d3U-w9-jNg" firstAttribute="leading" secondItem="7sy-3X-TvI" secondAttribute="leading" id="PAh-Wm-2kA"/>
                            <constraint firstItem="hWC-9z-cv3" firstAttribute="top" secondItem="ySw-Wg-Pgf" secondAttribute="bottom" constant="1" id="R7Z-q7-uy1"/>
                            <constraint firstItem="XJy-eX-prE" firstAttribute="leading" secondItem="Crp-4s-mEo" secondAttribute="leading" constant="47" id="TPK-B1-dpv"/>
                            <constraint firstItem="Lcx-bw-woE" firstAttribute="top" secondItem="d3U-w9-jNg" secondAttribute="bottom" constant="35" id="YWI-qf-QpZ"/>
                            <constraint firstItem="d3U-w9-jNg" firstAttribute="top" secondItem="7sy-3X-TvI" secondAttribute="bottom" constant="35" id="Yns-4x-PP5"/>
                            <constraint firstItem="7sy-3X-TvI" firstAttribute="leading" secondItem="ych-hK-1qT" secondAttribute="leading" id="dgY-s3-y2Y"/>
                            <constraint firstItem="ych-hK-1qT" firstAttribute="top" secondItem="App-Switcher-Recorder-View" secondAttribute="bottom" constant="10" id="dt7-ok-vmd"/>
                            <constraint firstItem="cIc-ga-45L" firstAttribute="top" secondItem="ych-hK-1qT" secondAttribute="bottom" constant="20" id="e9X-KM-xgc"/>
                            <constraint firstAttribute="trailing" secondItem="ych-hK-1qT" secondAttribute="trailing" constant="28" id="flW-eQ-HBq"/>
                            <constraint firstItem="7sy-3X-TvI" firstAttribute="top" secondItem="XJy-eX-prE" secondAttribute="bottom" constant="35" id="iev-sH-BcC"/>
                            <constraint firstItem="9pw-DH-pgZ" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="hWC-9z-cv3" secondAttribute="trailing" constant="8" symbolic="YES" id="j2v-d2-eIh"/>
                            <constraint firstItem="cIc-ga-45L" firstAttribute="baseline" secondItem="0zk-yi-rDJ" secondAttribute="firstBaseline" id="jQT-Gj-nXr"/>
                            <constraint firstItem="0zk-yi-rDJ" firstAttribute="baseline" secondItem="cIc-ga-45L" secondAttribute="firstBaseline" id="naB-jx-Dpc"/>
                            <constraint firstItem="ych-hK-1qT" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="cIc-ga-45L" secondAttribute="leading" id="okT-QH-OjF"/>
                            <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="7sy-3X-TvI" secondAttribute="trailing" constant="20" symbolic="YES" id="pAl-Ix-jpG"/>
                            <constraint firstItem="Lcx-bw-woE" firstAttribute="leading" secondItem="Crp-4s-mEo" secondAttribute="leading" constant="47" id="sle-yJ-9aF"/>
                            <constraint firstAttribute="bottom" secondItem="prA-Il-0qM" secondAttribute="bottom" constant="20" symbolic="YES" id="taQ-Tf-jfr"/>
                            <constraint firstItem="ySw-Wg-Pgf" firstAttribute="trailing" secondItem="9pw-DH-pgZ" secondAttribute="trailing" id="uVo-Rg-JBi"/>
                            <constraint firstItem="0zk-yi-rDJ" firstAttribute="leading" secondItem="cIc-ga-45L" secondAttribute="trailing" constant="6" id="vtd-KI-5AV"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="appSwitcherRecorderView" destination="App-Switcher-Recorder-View" id="App-Switcher-Recorder-Outlet"/>
                        <outlet property="btnEnableAppSwitcher" destination="App-Switcher-Enable-Btn" id="App-Switcher-Outlet"/>
                        <outlet property="btnEnableDeactivateKey" destination="ych-hK-1qT" id="wjv-C6-I04"/>
                        <outlet property="btnEnableMenuBarIcon" destination="d3U-w9-jNg" id="VhB-4g-cwa"/>
                        <outlet property="btnEnableMenuBarIconShowHideKey" destination="Lcx-bw-woE" id="ZxA-zv-c8A"/>
                        <outlet property="btnEnableShortcut" destination="7sy-3X-TvI" id="qGM-kS-fk6"/>
                        <outlet property="btnLaunchAtLogin" destination="XJy-eX-prE" id="xKD-J0-ALg"/>
                        <outlet property="btnShortcutDeactivateKey" destination="0zk-yi-rDJ" id="OzF-KE-TX0"/>
                        <outlet property="slider" destination="9mT-en-uZb" id="ZmK-N7-ClV"/>
                    </connections>
                </viewController>
                <userDefaultsController representsSharedInstance="YES" id="XlP-8s-PZm"/>
            </objects>
            <point key="canvasLocation" x="1545" y="416"/>
        </scene>
    </scenes>
    <resources>
        <image name="NSActionTemplate" width="20" height="20"/>
        <image name="NSAddTemplate" width="18" height="17"/>
        <image name="NSRemoveTemplate" width="18" height="5"/>
    </resources>
</document>
