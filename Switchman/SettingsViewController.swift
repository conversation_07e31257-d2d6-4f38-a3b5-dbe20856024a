//
//  SettingsViewController.swift
//  Switchman
//
//  Created by <PERSON><PERSON><PERSON> on 4/18/16.
//  Copyright © 2016 <PERSON><PERSON><PERSON>. All rights reserved.
//

import Cocoa
import LaunchAtLogin
import KeyboardShortcuts

class SettingsViewController: NSViewController {

    @IBOutlet weak var btnLaunchAtLogin: NSButton!
    @IBOutlet weak var btnEnableShortcut: NSButton!
    @IBOutlet weak var btnEnableMenuBarIcon: NSButton!
    @IBOutlet weak var btnEnableMenuBarIconShowHideKey: NSButton!
    @IBOutlet weak var btnEnableAppSwitcher: NSButton!
    @IBOutlet weak var appSwitcherRecorderView: NSView!
    @IBOutlet weak var btnEnableDeactivateKey: NSButton!
    @IBOutlet weak var btnShortcutDeactivateKey: NSPopUpButton!
    @IBOutlet weak var slider: NSSlider!

    private var appSwitcherRecorder: KeyboardShortcuts.RecorderCocoa?

    override func viewDidLoad() {
        super.viewDidLoad()

        view.layer?.backgroundColor = NSColor.clear.cgColor

        btnLaunchAtLogin.state = LaunchAtLogin.isEnabled ? .on : .off

        btnEnableShortcut.state = defaults[.EnableShortcut] ? .on : .off

        btnEnableMenuBarIcon.state = defaults[.enableMenuBarIcon] ? .on : .off
        btnEnableMenuBarIconShowHideKey.state = defaults[.enableMenuBarIconShowHideKey] ? .on : .off

        // App Switcher settings
        btnEnableAppSwitcher.state = defaults[.enableAppSwitcher] ? .on : .off
        setupAppSwitcherRecorder()

        let isEnableDeactivateKey = defaults[.EnableDeactivateKey]

        btnEnableDeactivateKey.state = isEnableDeactivateKey ? .on : .off

        btnShortcutDeactivateKey.selectItem(at: defaults[.DeactivateKey])
        btnShortcutDeactivateKey.isEnabled = isEnableDeactivateKey

        slider.doubleValue = defaults[.DelayInterval]
        slider.isEnabled = isEnableDeactivateKey

        NotificationCenter.default.addObserver(self,
                                               selector: #selector(updateMenuBarToggleState),
                                               name: .updateMenuBarToggleState,
                                               object: nil)
    }

    @IBAction func toggleLaunchAtLogin(_ sender: Any) {
        LaunchAtLogin.isEnabled = !LaunchAtLogin.isEnabled
    }

    @IBAction func toggleEnableShortcut(_ sender: Any) {
        let enable = btnEnableShortcut.state == .on

        defaults[.EnableShortcut] = enable

        if enable {
            ShortcutMonitor.register()
        } else {
            ShortcutMonitor.unregister()
        }
    }

    @IBAction func toggleEnableMenuBarIcon(_ sender: Any) {
        let enable = btnEnableMenuBarIcon.state == .on

        defaults[.enableMenuBarIcon] = enable

        if enable {
            sharedAppDelegate?.statusItemController.showInMenuBar()
        } else {
            sharedAppDelegate?.statusItemController.hideInMenuBar()
        }
    }

    @IBAction func toggleEnableMenuBarIconShowHideKey(_ sender: Any) {
        let enable = btnEnableMenuBarIconShowHideKey.state == .on

        defaults[.enableMenuBarIconShowHideKey] = enable

        if enable {
            sharedAppDelegate?.registerMenubarIconShortcut()
        } else {
            sharedAppDelegate?.unregisterMenubarIconShortcut()
        }
    }

    @IBAction func toggleEnableAppSwitcher(_ sender: Any) {
        let enable = btnEnableAppSwitcher.state == .on

        defaults[.enableAppSwitcher] = enable

        if enable {
            sharedAppDelegate?.registerAppSwitcherShortcut()
        } else {
            sharedAppDelegate?.unregisterAppSwitcherShortcut()
        }

        appSwitcherRecorder?.isEnabled = enable
    }

    @IBAction func toggleEnableDeactivateKey(_ sender: Any) {
        let isEnableDeactivateKey = btnEnableDeactivateKey.state == .on

        defaults[.EnableDeactivateKey] = isEnableDeactivateKey

        btnShortcutDeactivateKey.isEnabled = isEnableDeactivateKey
        slider.isEnabled = isEnableDeactivateKey
    }

    @IBAction func changeDeactivateKey(_ sender: Any) {
        defaults[.DeactivateKey] = btnShortcutDeactivateKey.indexOfSelectedItem
    }

    @IBAction func changeShortcutReactivateInterval(_ sender: Any) {
        defaults[.DelayInterval] = slider.doubleValue
    }

    @IBAction func exit(_ sender: Any) {
        NSApp.terminate(self)
    }

    @objc func updateMenuBarToggleState() {
        btnEnableMenuBarIcon.state = defaults[.enableMenuBarIcon] ? .on : .off
    }

    private func setupAppSwitcherRecorder() {
        // Remove existing recorder if any
        appSwitcherRecorder?.removeFromSuperview()

        // Create new recorder
        appSwitcherRecorder = KeyboardShortcuts.RecorderCocoa(for: .showAppSwitcher) { [weak self] _ in
            // Shortcut changed - no additional action needed as KeyboardShortcuts handles persistence
        }

        guard let recorder = appSwitcherRecorder else { return }

        // Configure recorder
        recorder.translatesAutoresizingMaskIntoConstraints = false
        recorder.isEnabled = defaults[.enableAppSwitcher]

        // Add to view
        appSwitcherRecorderView.addSubview(recorder)

        // Set up constraints
        NSLayoutConstraint.activate([
            recorder.leadingAnchor.constraint(equalTo: appSwitcherRecorderView.leadingAnchor),
            recorder.trailingAnchor.constraint(equalTo: appSwitcherRecorderView.trailingAnchor),
            recorder.topAnchor.constraint(equalTo: appSwitcherRecorderView.topAnchor),
            recorder.bottomAnchor.constraint(equalTo: appSwitcherRecorderView.bottomAnchor)
        ])
    }

}
