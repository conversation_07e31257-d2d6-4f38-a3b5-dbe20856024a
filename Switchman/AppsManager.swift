//
//  AppsManager.swift
//  Switchman
//
//  Created by <PERSON><PERSON><PERSON> on 4/18/16.
//  Copyright © 2016 <PERSON><PERSON><PERSON>. All rights reserved.
//

import Foundation
import KeyboardShortcuts
import OSLog

class AppsManager: NSObject {

    // MARK: Properties

    static let manager = AppsManager()

    @objc dynamic var selectedApps = [AppModel]()

    private var closure: (([AppModel]) -> Void)!

    private var selectedAppsFilePath: String {
        let dir = NSSearchPathForDirectoriesInDomains(.applicationSupportDirectory, .userDomainMask, true).first
        let key = kCFBundleNameKey as String

        guard let appSupportDir = dir, let appName = Bundle.main.infoDictionary![key] as? String else {
            return ""
        }

        var appDir = appSupportDir.appendingPathComponent(appName)
        if !FileManager.default.fileExists(atPath: appDir) {
            // Backward compatibility
            let backwardAppDir = appSupportDir.appendingPathComponent("Switchman")

            if !FileManager.default.fileExists(atPath: backwardAppDir) {
                _ = try? FileManager.default.createDirectory(atPath: appDir, withIntermediateDirectories: true)
            } else {
                appDir = backwardAppDir
            }
        }

        return appDir.appendingPathComponent("apps.json")
    }

    // MARK: Life cycle

    override init() {
        super.init()

        loadApps(from: selectedAppsFilePath)
    }

    // MARK: Actions

    // Existing save method: Handles general app persistence and shortcut re-registration.
    func save(_ app: AppModel?) {
        guard let app = app else { return }

        ShortcutMonitor.unregister()

        if selectedApps.contains(app) {
            // App already exists, its shortcut is managed by KeyboardShortcuts.
            // No need to re-append, just ensure it's saved by saveData.
        } else {
            // Add new app
            selectedApps.append(app)
        }

        if saveData(to: selectedAppsFilePath) {
            ShortcutMonitor.register()
        }
    }

    // New overloaded save method: Allows setting an initial shortcut when saving an app.
    // This is called by the `add` action in ShortcutListViewController.
    func save(_ app: AppModel?, shortcut: KeyboardShortcuts.Shortcut?) {
        guard let appModel = app else { return }

        // If a shortcut is provided, set it for the app's KeyboardShortcuts.Name.
        // The KeyboardShortcuts framework handles storing this.
        if let newShortcut = shortcut {
            appModel.shortcutName.shortcut = newShortcut
        } else {
            // If shortcut is nil, this typically means no specific shortcut is being set initially.
            // KeyboardShortcuts will manage the default state (usually no shortcut).
            // We do not need to explicitly set appModel.shortcutName.shortcut = nil here
            // unless we want to force clear it. For new additions, nil just means "not set yet".
        }

        // Call the other `save` method to handle the actual array modification,
        // persistence to disk, and global shortcut re-registration.
        self.save(appModel)
    }

    func delete(_ index: Int) {
        guard 0 <= index && index < selectedApps.count else { return }

        ShortcutMonitor.unregister()

        selectedApps.remove(at: index)

        if saveData(to: selectedAppsFilePath) {
            ShortcutMonitor.register()
        }
    }

    func loadApps(from path: String) {
        do {
            var data = try Data(contentsOf: URL(fileURLWithPath: path))
            if let apps = try JSONSerialization.jsonObject(with: data) as? [[String: String]] {
                selectedApps = apps.compactMap { AppModel(jsonValue: $0) }
            } else {
                data = try Data(contentsOf: URL(fileURLWithPath: path).deletingPathExtension())
                if let apps = try NSKeyedUnarchiver.unarchivedObject(ofClasses: [NSDictionary.self,
                                                                                 NSArray.self,
                                                                                 NSString.self],
                                                                     from: data) as? [NSDictionary] {
                    // Backward compatibility
                    selectedApps = apps.compactMap { AppModel(dict: $0) }
                }
            }
            _ = saveData(to: selectedAppsFilePath)
        } catch {
            Logger.app.error("can't load with err: \(error.localizedDescription), path: \(path)")
        }
    }

    func move(with indexes: [Int], to row: Int) {
        let apps = indexes.map { selectedApps[$0] }
        let target = row - indexes.filter { $0 < row }.count
        for (idx, index) in indexes.enumerated() {
            selectedApps.remove(at: index - idx)
        }
        selectedApps.insert(contentsOf: apps, at: target)

        _ = saveData(to: selectedAppsFilePath)
    }

    func saveData(to path: String) -> Bool {
        do {
            let apps = selectedApps.map { $0.encodeToJSONValue() }
            let encoded = try JSONSerialization.data(withJSONObject: apps, options: [.prettyPrinted, .sortedKeys])
            try encoded.write(to: URL(fileURLWithPath: path), options: [.atomic])
            return true
        } catch {
            Logger.app.error("can't save with err: \(error.localizedDescription), path: \(path)")
            return false
        }
    }

}
